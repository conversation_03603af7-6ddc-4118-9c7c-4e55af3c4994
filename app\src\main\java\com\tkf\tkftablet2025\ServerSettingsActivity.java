package com.tkf.tkftablet2025;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.textfield.TextInputEditText;
import com.tkf.tkftablet2025.config.AppConfig;
import com.tkf.tkftablet2025.config.ServerSettingsManager;

/**
 * Activity for managing server URL settings.
 * Allows users to view current URL, set custom URL, and reset to default.
 */
public class ServerSettingsActivity extends AppCompatActivity {

    private static final String TAG = "ServerSettingsActivity";
    public static final int RESULT_URL_CHANGED = 100;

    private AppConfig appConfig;
    private ServerSettingsManager serverSettingsManager;

    // UI Components
    private TextView currentUrlText;
    private TextView currentIpText;
    private TextView defaultIpText;
    private TextInputEditText serverIpEditText;
    private Button saveButton;
    private Button cancelButton;
    private Button resetButton;
    private FloatingActionButton closeFab;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Starting ServerSettingsActivity");
        setContentView(R.layout.activity_server_settings);

        // Initialize configuration
        appConfig = new AppConfig(this);
        serverSettingsManager = appConfig.getServerSettingsManager();
        Log.d(TAG, "onCreate: Configuration initialized");

        initializeViews();
        setupClickListeners();
        loadCurrentSettings();
    }

    /**
     * Initialize all views
     */
    private void initializeViews() {
        try {
            currentUrlText = findViewById(R.id.currentUrlText);
            currentIpText = findViewById(R.id.currentIpText);
            defaultIpText = findViewById(R.id.defaultIpText);
            serverIpEditText = findViewById(R.id.serverIpEditText);
            saveButton = findViewById(R.id.saveButton);
            cancelButton = findViewById(R.id.cancelButton);
            resetButton = findViewById(R.id.resetButton);
            closeFab = findViewById(R.id.closeFab);

            Log.d(TAG, "initializeViews: All views initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "initializeViews: Error initializing views", e);
        }
    }

    /**
     * Setup click listeners for all interactive elements
     */
    private void setupClickListeners() {
        Log.d(TAG, "setupClickListeners: Setting up click listeners");

        // Save button
        saveButton.setOnClickListener(v -> {
            Log.d(TAG, "Save button clicked");
            saveServerIp();
        });

        // Cancel button
        cancelButton.setOnClickListener(v -> {
            Log.d(TAG, "Cancel button clicked");
            finish();
        });

        // Reset button
        resetButton.setOnClickListener(v -> {
            Log.d(TAG, "Reset button clicked");
            resetToDefault();
        });

        // Close FAB
        closeFab.setOnClickListener(v -> {
            Log.d(TAG, "Close FAB clicked");
            finish();
        });
    }

    /**
     * Load current settings and display them
     */
    private void loadCurrentSettings() {
        Log.d(TAG, "loadCurrentSettings: Loading current settings");

        try {
            // Display current URL
            String currentUrl = appConfig.getBaseUrl();
            currentUrlText.setText(currentUrl);
            Log.d(TAG, "loadCurrentSettings: Current URL: " + currentUrl);

            // Display current IP (extracted from current URL or saved IP)
            String currentIp;
            String savedIp = serverSettingsManager.getSavedIp();
            if (savedIp != null && !savedIp.trim().isEmpty()) {
                currentIp = savedIp;
            } else {
                currentIp = serverSettingsManager.getDefaultIp();
            }
            currentIpText.setText(currentIp);
            Log.d(TAG, "loadCurrentSettings: Current IP: " + currentIp);

            // Display default IP
            String defaultIp = serverSettingsManager.getDefaultIp();
            defaultIpText.setText(defaultIp);
            Log.d(TAG, "loadCurrentSettings: Default IP: " + defaultIp);

            // Set input field with saved custom IP (if any)
            if (savedIp != null && !savedIp.trim().isEmpty()) {
                serverIpEditText.setText(savedIp);
                Log.d(TAG, "loadCurrentSettings: Loaded saved IP into input field");
            } else {
                // If no custom IP is saved, input field left empty
                serverIpEditText.setText("");
                Log.d(TAG, "loadCurrentSettings: No saved IP, input field left empty");
            }

        } catch (Exception e) {
            Log.e(TAG, "loadCurrentSettings: Error loading settings", e);
            showError("Ayarlar yüklenirken hata oluştu");
        }
    }

    /**
     * Save the server IP from input field
     */
    private void saveServerIp() {
        Log.d(TAG, "saveServerIp: Attempting to save server IP");

        try {
            String inputIp = serverIpEditText.getText().toString().trim();

            if (inputIp.isEmpty()) {
                showError("IP adresi boş olamaz");
                return;
            }

            // Validate IP format
            if (!ServerSettingsManager.isValidIp(inputIp)) {
                showError(getString(R.string.ip_validation_error));
                return;
            }

            // Save the IP
            boolean success = serverSettingsManager.saveServerIp(inputIp);

            if (success) {
                Log.d(TAG, "saveServerIp: IP saved successfully");
                showSuccess(getString(R.string.ip_saved_successfully));
                loadCurrentSettings(); // Refresh display

                // Set result to indicate URL was changed
                setResult(RESULT_URL_CHANGED);
            } else {
                Log.e(TAG, "saveServerIp: Failed to save IP");
                showError("IP kaydedilemedi");
            }

        } catch (Exception e) {
            Log.e(TAG, "saveServerIp: Error saving IP", e);
            showError("IP kaydedilirken hata oluştu");
        }
    }

    /**
     * Reset IP to default value
     */
    private void resetToDefault() {
        Log.d(TAG, "resetToDefault: Resetting IP to default");

        try {
            boolean success = serverSettingsManager.resetToDefault();

            if (success) {
                Log.d(TAG, "resetToDefault: IP reset successfully");
                showSuccess(getString(R.string.ip_reset_to_default));
                loadCurrentSettings(); // Refresh display

                // Set result to indicate URL was changed
                setResult(RESULT_URL_CHANGED);
            } else {
                Log.e(TAG, "resetToDefault: Failed to reset IP");
                showError("IP sıfırlanamadı");
            }

        } catch (Exception e) {
            Log.e(TAG, "resetToDefault: Error resetting IP", e);
            showError("IP sıfırlanırken hata oluştu");
        }
    }

    /**
     * Show success message
     */
    private void showSuccess(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_LONG)
                .setBackgroundTint(getResources().getColor(R.color.primary))
                .show();
    }

    /**
     * Show error message
     */
    private void showError(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_LONG)
                .setBackgroundTint(getResources().getColor(R.color.secondary))
                .show();
    }
}
