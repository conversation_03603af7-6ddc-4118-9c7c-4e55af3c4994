<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="32dp">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@android:drawable/ic_menu_manage"
            android:layout_marginEnd="16dp"
            app:tint="@color/primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/server_settings_title"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary" />

    </LinearLayout>

    <!-- Current URL Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/current_url"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/currentUrlText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="14sp"
                android:textColor="@color/black"
                android:background="@color/light_gray"
                android:padding="12dp"
                android:fontFamily="monospace" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Current IP Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/current_ip"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/currentIpText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="14sp"
                android:textColor="@color/black"
                android:background="@color/light_gray"
                android:padding="12dp"
                android:fontFamily="monospace" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- IP Input Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/server_ip_label"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/server_ip_hint"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary_variant">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/serverIpEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textUri"
                    android:fontFamily="monospace"
                    android:textSize="14sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/url_path_info"
                android:textSize="12sp"
                android:textColor="@color/secondary_dark"
                android:layout_marginTop="8dp"
                android:fontFamily="monospace" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Default IP Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/default_ip"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/defaultIpText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="14sp"
                android:textColor="@color/secondary_dark"
                android:background="@color/light_gray"
                android:padding="12dp"
                android:fontFamily="monospace" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Buttons Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="80dp">

        <Button
            android:id="@+id/resetButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="@string/reset_to_default"
            android:backgroundTint="@color/secondary"
            android:textColor="@color/white"
            android:padding="12dp" />

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:text="@string/cancel_button"
            android:backgroundTint="@color/secondary_dark"
            android:textColor="@color/white"
            android:padding="12dp" />

        <Button
            android:id="@+id/saveButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="@string/save_button"
            android:backgroundTint="@color/primary"
            android:textColor="@color/white"
            android:padding="12dp" />

        </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Close FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/closeFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/fab_margin"
        app:srcCompat="@android:drawable/ic_menu_close_clear_cancel"
        android:contentDescription="@string/close_button"
        app:backgroundTint="@color/secondary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
