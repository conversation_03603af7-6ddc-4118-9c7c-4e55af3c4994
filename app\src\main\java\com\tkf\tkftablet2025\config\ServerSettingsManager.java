package com.tkf.tkftablet2025.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import com.tkf.tkftablet2025.R;

/**
 * Manager class for handling server settings and URL configuration.
 * This class manages the storage and retrieval of server URL settings
 * using SharedPreferences for persistent storage.
 */
public class ServerSettingsManager {

    private static final String TAG = "ServerSettingsManager";
    private static final String PREFS_NAME = "server_settings";
    private static final String KEY_SERVER_IP = "server_ip";
    private static final String URL_PATH = "/TKFYarisma/Tablet/Interface/";

    private final Context context;
    private final SharedPreferences sharedPreferences;

    /**
     * Constructor for ServerSettingsManager
     * @param context Application context
     */
    public ServerSettingsManager(Context context) {
        this.context = context.getApplicationContext();
        this.sharedPreferences = this.context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        Log.d(TAG, "ServerSettingsManager initialized");
    }

    /**
     * Get the current server URL. Returns saved URL if available, otherwise returns default URL.
     * @return Server URL string
     */
    public String getServerUrl() {
        String savedIp = sharedPreferences.getString(KEY_SERVER_IP, null);

        if (savedIp != null && !savedIp.trim().isEmpty()) {
            String fullUrl = buildFullUrl(savedIp);
            Log.d(TAG, "getServerUrl: Using saved IP: " + savedIp + " -> " + fullUrl);
            return fullUrl;
        } else {
            String defaultUrl = getDefaultServerUrl();
            Log.d(TAG, "getServerUrl: Using default URL: " + defaultUrl);
            return defaultUrl;
        }
    }

    /**
     * Save the server IP address to SharedPreferences
     * @param ip Server IP address to save
     * @return true if saved successfully, false otherwise
     */
    public boolean saveServerIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            Log.w(TAG, "saveServerIp: IP is null or empty");
            return false;
        }

        String cleanIp = ip.trim();

        // Remove http:// or https:// if present
        if (cleanIp.startsWith("http://")) {
            cleanIp = cleanIp.substring(7);
        } else if (cleanIp.startsWith("https://")) {
            cleanIp = cleanIp.substring(8);
        }

        // Remove trailing slash if present
        if (cleanIp.endsWith("/")) {
            cleanIp = cleanIp.substring(0, cleanIp.length() - 1);
        }

        try {
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString(KEY_SERVER_IP, cleanIp);
            boolean success = editor.commit();

            if (success) {
                Log.d(TAG, "saveServerIp: IP saved successfully: " + cleanIp);
            } else {
                Log.e(TAG, "saveServerIp: Failed to save IP");
            }

            return success;
        } catch (Exception e) {
            Log.e(TAG, "saveServerIp: Error saving IP", e);
            return false;
        }
    }

    /**
     * Reset server IP to default value
     * @return true if reset successfully, false otherwise
     */
    public boolean resetToDefault() {
        try {
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.remove(KEY_SERVER_IP);
            boolean success = editor.commit();

            if (success) {
                Log.d(TAG, "resetToDefault: IP reset to default successfully");
            } else {
                Log.e(TAG, "resetToDefault: Failed to reset IP");
            }

            return success;
        } catch (Exception e) {
            Log.e(TAG, "resetToDefault: Error resetting IP", e);
            return false;
        }
    }

    /**
     * Get the default server URL from resources
     * @return Default server URL string
     */
    public String getDefaultServerUrl() {
        try {
            // Use production URL as default
            return context.getString(R.string.production_base_url);
        } catch (Exception e) {
            Log.e(TAG, "getDefaultServerUrl: Error getting default URL from resources", e);
            // Fallback hardcoded URL
            return "http://*************/TKFYarisma/Tablet/Interface/";
        }
    }

    /**
     * Check if a custom IP is currently saved
     * @return true if custom IP is saved, false if using default
     */
    public boolean hasCustomIp() {
        String savedIp = sharedPreferences.getString(KEY_SERVER_IP, null);
        boolean hasCustom = savedIp != null && !savedIp.trim().isEmpty();
        Log.d(TAG, "hasCustomIp: " + hasCustom);
        return hasCustom;
    }

    /**
     * Validate IP address format
     * @param ip IP address to validate
     * @return true if IP format is valid, false otherwise
     */
    public static boolean isValidIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        String cleanIp = ip.trim();

        // Remove http:// or https:// if present
        if (cleanIp.startsWith("http://")) {
            cleanIp = cleanIp.substring(7);
        } else if (cleanIp.startsWith("https://")) {
            cleanIp = cleanIp.substring(8);
        }

        // Remove trailing slash if present
        if (cleanIp.endsWith("/")) {
            cleanIp = cleanIp.substring(0, cleanIp.length() - 1);
        }

        // Basic IP validation (IPv4 format)
        String[] parts = cleanIp.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Get saved IP without fallback to default (for display purposes)
     * @return Saved IP or null if not set
     */
    public String getSavedIp() {
        return sharedPreferences.getString(KEY_SERVER_IP, null);
    }

    /**
     * Get default IP address extracted from default URL
     * @return Default IP address
     */
    public String getDefaultIp() {
        String defaultUrl = getDefaultServerUrl();
        // Extract IP from http://*************/TKFYarisma/Tablet/Interface/
        if (defaultUrl.startsWith("http://")) {
            String withoutProtocol = defaultUrl.substring(7);
            int slashIndex = withoutProtocol.indexOf('/');
            if (slashIndex > 0) {
                return withoutProtocol.substring(0, slashIndex);
            }
        }
        return "*************"; // Fallback
    }

    /**
     * Build full URL from IP address
     * @param ip IP address
     * @return Full URL
     */
    private String buildFullUrl(String ip) {
        return "http://" + ip + URL_PATH;
    }
}
