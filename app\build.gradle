plugins {
    id 'com.android.application'
}

android {
    namespace 'com.tkf.tkftablet2025'
    compileSdk 35

    defaultConfig {
        applicationId 'com.tkf.tkftablet2025'
        minSdk 21
        targetSdk 35
        versionCode 2
        versionName '1.1'

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        // Ensure debug builds are not marked as test-only
        testApplicationId 'com.tkf.tkftablet2025.test'
    }

    signingConfigs {
        debug {
            storeFile file(System.getProperty("user.home") + "/.android/debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
        release {
            // Use debug keystore for release builds (for development/testing)
            storeFile file(System.getProperty("user.home") + "/.android/debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
    }

    buildTypes {
        debug {
            debuggable true
            testCoverageEnabled false
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            buildConfigField "String", "BASE_URL", "\"http://10.0.2.2/TKFYarisma/Tablet/Interface/\""
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
            signingConfig signingConfigs.debug
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BASE_URL", "\"http://192.168.2.120/TKFYarisma/Tablet/Interface/\""
            buildConfigField "boolean", "ENABLE_LOGGING", "false"
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding true
        buildConfig true
        dataBinding false
    }

    // Custom APK naming
    applicationVariants.all { variant ->
        variant.outputs.all {
            def appName = "TKFTablet2025"
            def buildType = variant.buildType.name
            def versionName = variant.versionName
            def versionCode = variant.versionCode
            def date = new Date().format('yyyyMMdd')

            if (buildType == "release") {
                outputFileName = "${appName}_v${versionName}_${date}_release.apk"
            } else if (buildType == "debug") {
                outputFileName = "${appName}_v${versionName}_${date}_debug.apk"
            }
        }
    }
}

dependencies {
    // AndroidX Core and AppCompat
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.12.0'

    // Material Design
    implementation 'com.google.android.material:material:1.11.0'

    // ConstraintLayout
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // Lifecycle components
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.6.2'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
