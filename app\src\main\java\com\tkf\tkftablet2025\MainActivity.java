package com.tkf.tkftablet2025;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.snackbar.Snackbar;
import com.tkf.tkftablet2025.config.AppConfig;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private static final int REQUEST_SERVER_SETTINGS = 1001;

    private WebView webView;
    private AppConfig appConfig;
    private LinearLayout loadingOverlay;
    private ImageView skateboardImage;
    private ProgressBar progressBar;
    private Animation skateboardAnimation;
    private boolean isLoadingVisible = false; // Track loading state

    // FAB Menu components
    private FloatingActionButton fabMain;
    private FloatingActionButton fabReload;
    private FloatingActionButton fabIpShow;
    private FloatingActionButton fabServerSettings;
    private TextView fabReloadLabel;
    private TextView fabIpLabel;
    private TextView fabServerSettingsLabel;
    private boolean isFabMenuOpen = false;
    private Animation fabRotateForward;
    private Animation fabRotateBackward;
    private Animation fabScaleUp;
    private Animation fabScaleDown;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Starting MainActivity");
        setContentView(R.layout.activity_main);

        // Initialize configuration
        appConfig = new AppConfig(this);
        Log.d(TAG, "onCreate: AppConfig initialized");
        Log.d(TAG, "onCreate: Debug mode: " + appConfig.isDebugMode());
        Log.d(TAG, "onCreate: Base URL: " + appConfig.getBaseUrl());

        // Initialize views
        initializeViews();
        Log.d(TAG, "onCreate: Views initialized");

        // Initialize WebView
        webView = findViewById(R.id.webView);
        if (webView != null) {
            setupWebView();
            Log.d(TAG, "onCreate: WebView setup completed");
        } else {
            Log.e(TAG, "onCreate: WebView is null!");
        }

        // Start skateboard animation
        startSkateboardAnimation();

        // Set up FAB menu
        setupFabMenu();

        // Set up modern back press handling
        setupBackPressHandling();

        // Clear cache and load the target URL
        if (webView != null) {
            webView.clearCache(true);
            String url = appConfig.getBaseUrl();
            Log.d(TAG, "onCreate: Loading URL: " + url);
            webView.loadUrl(url);
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        // Configure WebView settings
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(appConfig.isJavaScriptEnabled());
        webSettings.setDomStorageEnabled(appConfig.isDomStorageEnabled());
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setDisplayZoomControls(false);
        webSettings.setCacheMode(appConfig.isCacheEnabled() ? WebSettings.LOAD_DEFAULT : WebSettings.LOAD_NO_CACHE);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setLoadsImagesAutomatically(true);
        webSettings.setUserAgentString(appConfig.getUserAgent());

        // Set WebViewClient to handle page loading
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d(TAG, "onPageStarted: " + url);
                // Show loading animation when page starts loading
                showLoading();
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "onPageFinished: " + url);
                // Hide loading animation when page finishes loading
                hideLoading();
            }

            @Override
            public void onPageCommitVisible(WebView view, String url) {
                super.onPageCommitVisible(view, url);
                Log.d(TAG, "onPageCommitVisible: " + url);
                // Additional safety: hide loading when page becomes visible
                // This handles cases where onPageFinished might not be called
                hideLoading();
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);

                // Handle error logging based on Android version
                String errorMessage;
                String url;

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    // API 23+ - getDescription() is available
                    errorMessage = error.getDescription().toString();
                    url = request.getUrl().toString();
                } else {
                    // API 21-22 - getDescription() is not available
                    errorMessage = "WebView error (error code not available on API < 23)";
                    url = request != null && request.getUrl() != null ? request.getUrl().toString() : "unknown URL";
                }

                Log.e(TAG, "onReceivedError: " + errorMessage + " for URL: " + url);

                // Hide loading and show error message
                hideLoading();
                Toast.makeText(MainActivity.this, R.string.error_loading_page, Toast.LENGTH_LONG).show();
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                Log.d(TAG, "shouldOverrideUrlLoading: " + request.getUrl());
                // Show loading animation for new URLs
                showLoading();
                // Clear cache before loading new URLs
                view.clearCache(true);
                return super.shouldOverrideUrlLoading(view, request);
            }
        });

        // Set WebChromeClient for handling JavaScript dialogs, favicons, titles, and progress
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                // Update progress bar
                updateProgress(newProgress);
            }
        });
    }

    /**
     * Set up modern back press handling using OnBackPressedDispatcher
     * This replaces the deprecated onBackPressed() method and supports API 21+
     */
    private void setupBackPressHandling() {
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                Log.d(TAG, "handleOnBackPressed: Back button pressed");

                // First priority: Close FAB menu if open
                if (isFabMenuOpen) {
                    Log.d(TAG, "handleOnBackPressed: Closing FAB menu");
                    closeFabMenu();
                    return;
                }

                // Second priority: Handle WebView navigation
                if (webView != null && webView.canGoBack()) {
                    Log.d(TAG, "handleOnBackPressed: Navigating back in WebView");
                    webView.goBack();
                } else {
                    Log.d(TAG, "handleOnBackPressed: No WebView history, performing default back action");
                    // Disable this callback temporarily and trigger default back behavior
                    setEnabled(false);
                    getOnBackPressedDispatcher().onBackPressed();
                }
            }
        });
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (webView != null) {
            webView.onPause();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (webView != null) {
            webView.onResume();
            // Manual reload available via FAB menu - no automatic reload needed
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_SERVER_SETTINGS && resultCode == ServerSettingsActivity.RESULT_URL_CHANGED) {
            Log.d(TAG, "onActivityResult: Server URL was changed, reloading WebView");

            // URL was changed, reload WebView with new URL
            if (webView != null) {
                webView.clearCache(true);
                String newUrl = appConfig.getBaseUrl();
                Log.d(TAG, "onActivityResult: Loading new URL: " + newUrl);
                webView.loadUrl(newUrl);
            }
        }
    }

    @Override
    protected void onDestroy() {
        // Stop animations
        if (skateboardAnimation != null) {
            skateboardAnimation.cancel();
        }
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }

    /**
     * Initialize all views and animations
     */
    private void initializeViews() {
        try {
            loadingOverlay = findViewById(R.id.loadingOverlay);
            skateboardImage = findViewById(R.id.skateboardImage);
            progressBar = findViewById(R.id.progressBar);

            // Load skateboard animation
            skateboardAnimation = AnimationUtils.loadAnimation(this, R.anim.skateboard_slide);

            // Initialize FAB menu components
            fabMain = findViewById(R.id.fabMain);
            fabReload = findViewById(R.id.fabReload);
            fabIpShow = findViewById(R.id.fabIpShow);
            fabServerSettings = findViewById(R.id.fabServerSettings);
            fabReloadLabel = findViewById(R.id.fabReloadLabel);
            fabIpLabel = findViewById(R.id.fabIpLabel);
            fabServerSettingsLabel = findViewById(R.id.fabServerSettingsLabel);

            // Load FAB animations
            fabRotateForward = AnimationUtils.loadAnimation(this, R.anim.fab_rotate_forward);
            fabRotateBackward = AnimationUtils.loadAnimation(this, R.anim.fab_rotate_backward);
            fabScaleUp = AnimationUtils.loadAnimation(this, R.anim.fab_scale_up);
            fabScaleDown = AnimationUtils.loadAnimation(this, R.anim.fab_scale_down);

            Log.d(TAG, "initializeViews: All views initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "initializeViews: Error initializing views", e);
        }
    }

    /**
     * Start the skateboard sliding animation
     */
    private void startSkateboardAnimation() {
        if (skateboardImage != null && skateboardAnimation != null) {
            skateboardImage.startAnimation(skateboardAnimation);
        }
    }

    /**
     * Show loading overlay with skateboard animation
     */
    private void showLoading() {
        if (isLoadingVisible) {
            Log.d(TAG, "showLoading: Loading already visible, skipping");
            return;
        }

        Log.d(TAG, "showLoading: Showing loading animation");
        isLoadingVisible = true;

        if (loadingOverlay != null) {
            loadingOverlay.setVisibility(View.VISIBLE);
            startSkateboardAnimation();
        }
        if (progressBar != null) {
            progressBar.setVisibility(View.VISIBLE);
            progressBar.setProgress(0);
        }
    }

    /**
     * Hide loading overlay and stop animations
     */
    private void hideLoading() {
        if (!isLoadingVisible) {
            Log.d(TAG, "hideLoading: Loading already hidden, skipping");
            return;
        }

        Log.d(TAG, "hideLoading: Hiding loading animation");
        isLoadingVisible = false;

        if (loadingOverlay != null) {
            loadingOverlay.setVisibility(View.GONE);
        }
        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }
        if (skateboardImage != null) {
            skateboardImage.clearAnimation();
        }
    }

    /**
     * Update progress bar with loading progress
     */
    private void updateProgress(int progress) {
        if (progressBar != null) {
            progressBar.setProgress(progress);

            // Show progress bar when loading starts
            if (progress > 0 && progress < 100) {
                progressBar.setVisibility(View.VISIBLE);
            }
            // Hide progress bar when loading completes
            else if (progress >= 100) {
                progressBar.setVisibility(View.GONE);
            }
        }
    }

    /**
     * Setup FAB menu system
     */
    private void setupFabMenu() {
        Log.d(TAG, "setupFabMenu: Setting up FAB menu");

        // Main FAB click listener
        fabMain.setOnClickListener(v -> {
            Log.d(TAG, "Main FAB clicked");
            if (isFabMenuOpen) {
                closeFabMenu();
            } else {
                openFabMenu();
            }
        });

        // Reload FAB click listener
        fabReload.setOnClickListener(v -> {
            Log.d(TAG, "Reload FAB clicked: Reloading page");
            closeFabMenu();

            // Reload WebView
            if (webView != null) {
                webView.clearCache(true);
                webView.reload();
            }
            Snackbar.make(v, R.string.loading, Snackbar.LENGTH_SHORT).show();
        });

        // IP Show FAB click listener
        fabIpShow.setOnClickListener(v -> {
            Log.d(TAG, "IP Show FAB clicked: Opening IP Address Activity");
            closeFabMenu();

            // Start IP Address Activity
            Intent intent = new Intent(MainActivity.this, IPAddressActivity.class);
            startActivity(intent);
        });

        // Server Settings FAB click listener
        fabServerSettings.setOnClickListener(v -> {
            Log.d(TAG, "Server Settings FAB clicked: Opening Server Settings Activity");
            closeFabMenu();

            // Start Server Settings Activity for result
            Intent intent = new Intent(MainActivity.this, ServerSettingsActivity.class);
            startActivityForResult(intent, REQUEST_SERVER_SETTINGS);
        });
    }

    /**
     * Open FAB menu with animations
     */
    private void openFabMenu() {
        Log.d(TAG, "openFabMenu: Opening FAB menu");
        isFabMenuOpen = true;

        // Rotate main FAB
        fabMain.startAnimation(fabRotateForward);

        // Show and animate reload FAB
        fabReload.setVisibility(View.VISIBLE);
        fabReload.setClickable(true);
        fabReload.setAlpha(1f);
        fabReloadLabel.setVisibility(View.VISIBLE);
        fabReload.startAnimation(fabScaleUp);
        fabReloadLabel.animate().alpha(1f).setDuration(200).start();

        // Show and animate server settings FAB with delay
        fabServerSettings.postDelayed(() -> {
            fabServerSettings.setVisibility(View.VISIBLE);
            fabServerSettings.setClickable(true);
            fabServerSettings.setAlpha(1f);
            fabServerSettingsLabel.setVisibility(View.VISIBLE);
            fabServerSettings.startAnimation(fabScaleUp);
            fabServerSettingsLabel.animate().alpha(1f).setDuration(200).start();
        }, 50);

        // Show and animate IP show FAB with delay
        fabIpShow.postDelayed(() -> {
            fabIpShow.setVisibility(View.VISIBLE);
            fabIpShow.setClickable(true);
            fabIpShow.setAlpha(1f);
            fabIpLabel.setVisibility(View.VISIBLE);
            fabIpShow.startAnimation(fabScaleUp);
            fabIpLabel.animate().alpha(1f).setDuration(200).start();
        }, 100);
    }

    /**
     * Close FAB menu with animations
     */
    private void closeFabMenu() {
        Log.d(TAG, "closeFabMenu: Closing FAB menu");
        isFabMenuOpen = false;

        // Rotate main FAB back
        fabMain.startAnimation(fabRotateBackward);

        // Hide and animate reload FAB
        fabReload.setClickable(false);
        fabReload.startAnimation(fabScaleDown);
        fabReloadLabel.animate().alpha(0f).setDuration(150).start();
        fabReload.postDelayed(() -> {
            fabReload.setVisibility(View.GONE);
            fabReload.setAlpha(0f);
        }, 150);
        fabReloadLabel.postDelayed(() -> fabReloadLabel.setVisibility(View.GONE), 150);

        // Hide and animate server settings FAB
        fabServerSettings.setClickable(false);
        fabServerSettings.startAnimation(fabScaleDown);
        fabServerSettingsLabel.animate().alpha(0f).setDuration(150).start();
        fabServerSettings.postDelayed(() -> {
            fabServerSettings.setVisibility(View.GONE);
            fabServerSettings.setAlpha(0f);
        }, 150);
        fabServerSettingsLabel.postDelayed(() -> fabServerSettingsLabel.setVisibility(View.GONE), 150);

        // Hide and animate IP show FAB
        fabIpShow.setClickable(false);
        fabIpShow.startAnimation(fabScaleDown);
        fabIpLabel.animate().alpha(0f).setDuration(150).start();
        fabIpShow.postDelayed(() -> {
            fabIpShow.setVisibility(View.GONE);
            fabIpShow.setAlpha(0f);
        }, 150);
        fabIpLabel.postDelayed(() -> fabIpLabel.setVisibility(View.GONE), 150);
    }


}
