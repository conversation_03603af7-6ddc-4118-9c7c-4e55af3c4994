<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".IPAddressActivity">

    <!-- Main Content with ScrollView -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:paddingBottom="80dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="32dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@android:drawable/ic_dialog_info"
                android:tint="@color/primary"
                android:layout_marginEnd="16dp"
                android:contentDescription="Network info icon" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/network_information"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/device_ip_addresses"
                    android:textSize="14sp"
                    android:textColor="@color/primary_variant"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Connection Status -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/connection_status"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/wifiStatusText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/wifi_disconnected"
                        android:textSize="14sp"
                        android:textColor="@color/secondary_dark"
                        android:drawableStart="@android:drawable/ic_dialog_alert"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                    <TextView
                        android:id="@+id/mobileStatusText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/mobile_disconnected"
                        android:textSize="14sp"
                        android:textColor="@color/secondary_dark"
                        android:drawableStart="@android:drawable/ic_dialog_alert"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- IP Addresses List -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="300dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/ip_addresses"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary" />

                    <Button
                        android:id="@+id/refreshButton"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="@string/refresh"
                        android:textSize="12sp"
                        android:backgroundTint="@color/secondary"
                        android:textColor="@color/white"
                        style="@style/Widget.Material3.Button.UnelevatedButton" />

                </LinearLayout>

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxHeight="200dp">

                    <LinearLayout
                        android:id="@+id/ipAddressContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- IP addresses will be added here dynamically -->
                        <TextView
                            android:id="@+id/noIpAddressText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/no_ip_addresses_found"
                            android:textSize="14sp"
                            android:textColor="@color/primary_variant"
                            android:gravity="center"
                            android:padding="32dp"
                            android:visibility="gone" />

                    </LinearLayout>

                </ScrollView>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- Close FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/closeFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/fab_margin"
        app:srcCompat="@android:drawable/ic_menu_close_clear_cancel"
        android:contentDescription="@string/close_button"
        app:backgroundTint="@color/secondary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
