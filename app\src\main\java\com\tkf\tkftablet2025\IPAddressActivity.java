package com.tkf.tkftablet2025;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.tkf.tkftablet2025.utils.NetworkUtils;
import java.util.List;

/**
 * Activity to display device IP addresses and network information
 */
public class IPAddressActivity extends AppCompatActivity {

    private static final String TAG = "IPAddressActivity";

    private LinearLayout ipAddressContainer;
    private TextView noIpAddressText;
    private TextView wifiStatusText;
    private TextView mobileStatusText;
    private FloatingActionButton closeFab;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Starting IPAddressActivity");
        setContentView(R.layout.activity_ip_address);

        initializeViews();
        setupClickListeners();
        loadNetworkInformation();
    }

    /**
     * Initialize all views
     */
    private void initializeViews() {
        ipAddressContainer = findViewById(R.id.ipAddressContainer);
        noIpAddressText = findViewById(R.id.noIpAddressText);
        wifiStatusText = findViewById(R.id.wifiStatusText);
        mobileStatusText = findViewById(R.id.mobileStatusText);
        closeFab = findViewById(R.id.closeFab);

        Log.d(TAG, "initializeViews: Views initialized");
    }

    /**
     * Setup click listeners
     */
    private void setupClickListeners() {
        // Close button
        closeFab.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            finish();
        });

        // Refresh button
        findViewById(R.id.refreshButton).setOnClickListener(v -> {
            Log.d(TAG, "Refresh button clicked");
            loadNetworkInformation();
        });
    }

    /**
     * Load and display network information
     */
    private void loadNetworkInformation() {
        Log.d(TAG, "loadNetworkInformation: Loading network info");

        // Update connection status
        updateConnectionStatus();

        // Clear previous IP addresses
        ipAddressContainer.removeAllViews();

        // Get all IP addresses
        List<NetworkUtils.IPAddressInfo> ipAddresses = NetworkUtils.getAllIPAddresses(this);

        if (ipAddresses.isEmpty()) {
            noIpAddressText.setVisibility(View.VISIBLE);
            Log.d(TAG, "loadNetworkInformation: No IP addresses found");
        } else {
            noIpAddressText.setVisibility(View.GONE);

            // Add each IP address to the container
            for (NetworkUtils.IPAddressInfo ipInfo : ipAddresses) {
                addIPAddressView(ipInfo);
            }

            Log.d(TAG, "loadNetworkInformation: Added " + ipAddresses.size() + " IP addresses");
        }
    }

    /**
     * Update connection status indicators
     */
    private void updateConnectionStatus() {
        boolean isWifiConnected = NetworkUtils.isWiFiConnected(this);
        boolean isMobileConnected = NetworkUtils.isMobileDataConnected(this);

        // Update WiFi status
        if (isWifiConnected) {
            wifiStatusText.setText(R.string.wifi_connected);
            wifiStatusText.setTextColor(ContextCompat.getColor(this, R.color.primary));
            wifiStatusText.setCompoundDrawablesWithIntrinsicBounds(
                android.R.drawable.ic_dialog_info, 0, 0, 0);
        } else {
            wifiStatusText.setText(R.string.wifi_disconnected);
            wifiStatusText.setTextColor(ContextCompat.getColor(this, R.color.secondary_dark));
            wifiStatusText.setCompoundDrawablesWithIntrinsicBounds(
                android.R.drawable.ic_dialog_alert, 0, 0, 0);
        }

        // Update mobile data status
        if (isMobileConnected) {
            mobileStatusText.setText(R.string.mobile_connected);
            mobileStatusText.setTextColor(ContextCompat.getColor(this, R.color.primary));
            mobileStatusText.setCompoundDrawablesWithIntrinsicBounds(
                android.R.drawable.ic_dialog_info, 0, 0, 0);
        } else {
            mobileStatusText.setText(R.string.mobile_disconnected);
            mobileStatusText.setTextColor(ContextCompat.getColor(this, R.color.secondary_dark));
            mobileStatusText.setCompoundDrawablesWithIntrinsicBounds(
                android.R.drawable.ic_dialog_alert, 0, 0, 0);
        }

        Log.d(TAG, "updateConnectionStatus: WiFi=" + isWifiConnected + ", Mobile=" + isMobileConnected);
    }

    /**
     * Add an IP address view to the container
     * @param ipInfo IP address information
     */
    private void addIPAddressView(NetworkUtils.IPAddressInfo ipInfo) {
        // Create card view for each IP address
        CardView cardView = new CardView(this);
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        cardParams.setMargins(0, 0, 0, 16);
        cardView.setLayoutParams(cardParams);
        cardView.setCardElevation(2f);
        cardView.setRadius(8f);

        // Create inner layout
        LinearLayout innerLayout = new LinearLayout(this);
        innerLayout.setOrientation(LinearLayout.VERTICAL);
        innerLayout.setPadding(16, 12, 16, 12);

        // Create type text view
        TextView typeText = new TextView(this);
        typeText.setText(ipInfo.getType());
        typeText.setTextSize(12f);
        typeText.setTextColor(ContextCompat.getColor(this, R.color.primary_variant));
        typeText.setTypeface(typeText.getTypeface(), android.graphics.Typeface.BOLD);

        // Create IP address text view
        TextView ipText = new TextView(this);
        ipText.setText(ipInfo.getAddress());
        ipText.setTextSize(16f);
        ipText.setTextColor(ContextCompat.getColor(this, ipInfo.isPrimary() ? R.color.primary : R.color.black));
        ipText.setTypeface(ipText.getTypeface(), ipInfo.isPrimary() ? android.graphics.Typeface.BOLD : android.graphics.Typeface.NORMAL);

        // Add views to inner layout
        innerLayout.addView(typeText);
        innerLayout.addView(ipText);

        // Add inner layout to card
        cardView.addView(innerLayout);

        // Add card to container
        ipAddressContainer.addView(cardView);

        Log.d(TAG, "addIPAddressView: Added " + ipInfo.getType() + " - " + ipInfo.getAddress());
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Refresh network information when activity resumes
        loadNetworkInformation();
    }
}
