# Built application files
*.apk
*.aar
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# IntelliJ
*.iml
.idea/

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# External native build folder generated in Android Studio
.externalNativeBuild
.cxx/

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# Version control
vcs.xml

# lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/

# Android Profiling
*.hprof

# Keystore files
*.jks
*.keystore

# Release signing
release.properties

# VS Code specific files
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# VS Code workspace files
*.code-workspace

# VS Code history
.history/

# VS Code temporary files
*.vsix

# Java extension specific
.project
.classpath
.factorypath
.settings/
.vscode/settings.json.bak

# Language server workspace
.metadata/
.recommenders/

# Eclipse JDT Language Server workspace
.eclipse/

# Red Hat Java Language Server workspace
.redhat/

# Microsoft Java Language Server workspace
.vscode-java/

# Java debugging
*.launch

# Maven wrapper (if using Maven)
.mvn/wrapper/maven-wrapper.jar

# Gradle wrapper validation
gradle/wrapper/gradle-wrapper.jar.asc
