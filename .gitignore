# Built application files
*.apk
*.aar
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/
build/

# Gradle files
.gradle/
gradle-app.setting

# Local configuration file (sdk path, etc)
local.properties

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# IntelliJ
*.iml
.idea/
*.iws
*.ipr

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# External native build folder generated in Android Studio
.externalNativeBuild
.cxx/

# NDK
obj/

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# Version control
vcs.xml

# lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/

# Android Profiling
*.hprof

# Keystore files
*.jks
*.keystore

# Release signing
release.properties

# Backup files
*.bak
*~

# Temporary files
*.tmp
*.temp

# Android Studio 3.0+ serialized cache file
.idea/caches/build_file_checksums.ser

# Android Studio 3.1+ serialized cache file
.idea/libraries/

# Android Studio 3.5+ serialized cache file
.idea/jarRepositories.xml

# Gradle Wrapper
!gradle/wrapper/gradle-wrapper.jar
!gradle/wrapper/gradle-wrapper.properties

# VS Code specific files
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# VS Code workspace files
*.code-workspace

# VS Code history
.history/

# VS Code temporary files
*.vsix

# Java extension specific
.project
.classpath
.factorypath
.settings/
.vscode/settings.json.bak

# Language server workspace
.metadata/
.recommenders/

# Eclipse JDT Language Server workspace
.eclipse/

# Red Hat Java Language Server workspace
.redhat/

# Microsoft Java Language Server workspace
.vscode-java/

# Java debugging
*.launch

# Maven wrapper (if using Maven)
.mvn/wrapper/maven-wrapper.jar

# Gradle wrapper validation
gradle/wrapper/gradle-wrapper.jar.asc
