<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_gravity="top"
        android:progressTint="@color/primary"
        android:progressBackgroundTint="@color/primary_variant"
        android:visibility="gone"
        android:max="100"
        android:progress="0" />

    <!-- Loading Overlay -->
    <LinearLayout
        android:id="@+id/loadingOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible">

        <!-- Skateboard Animation Container -->
        <FrameLayout
            android:layout_width="200dp"
            android:layout_height="80dp"
            android:layout_marginBottom="24dp">

            <ImageView
                android:id="@+id/skateboardImage"
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:src="@drawable/skateboard_simple"
                android:contentDescription="Loading skateboard" />

        </FrameLayout>

        <TextView
            android:id="@+id/loadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/loading_skateboard"
            android:textColor="@color/primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/loading_subtitle"
            android:textColor="@color/primary_variant"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- FAB Menu Container -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/fab_margin"
        android:orientation="vertical"
        android:gravity="end">

        <!-- Server Settings FAB -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/fabServerSettingsLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fab_menu_server_settings"
                android:textColor="@color/white"
                android:background="@color/primary"
                android:padding="8dp"
                android:layout_marginEnd="16dp"
                android:textSize="12sp"
                android:elevation="4dp"
                android:visibility="gone"
                android:alpha="0" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabServerSettings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@android:drawable/ic_menu_manage"
                android:contentDescription="@string/fab_menu_server_settings"
                app:backgroundTint="@color/secondary"
                app:fabSize="mini"
                android:visibility="gone" />

        </LinearLayout>

        <!-- IP Show FAB -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/fabIpLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fab_menu_ip_show"
                android:textColor="@color/white"
                android:background="@color/primary"
                android:padding="8dp"
                android:layout_marginEnd="16dp"
                android:textSize="12sp"
                android:elevation="4dp"
                android:visibility="gone"
                android:alpha="0" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabIpShow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@android:drawable/ic_dialog_info"
                android:contentDescription="@string/fab_menu_ip_show"
                app:backgroundTint="@color/secondary"
                app:fabSize="mini"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Reload FAB -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/fabReloadLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fab_menu_reload"
                android:textColor="@color/white"
                android:background="@color/primary"
                android:padding="8dp"
                android:layout_marginEnd="16dp"
                android:textSize="12sp"
                android:elevation="4dp"
                android:visibility="gone"
                android:alpha="0" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabReload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@android:drawable/ic_menu_rotate"
                android:contentDescription="@string/fab_menu_reload"
                app:backgroundTint="@color/secondary"
                app:fabSize="mini"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Main FAB -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabMain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@android:drawable/ic_menu_add"
            android:contentDescription="@string/fab_menu_main"
            app:backgroundTint="@color/primary" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
