package com.tkf.tkftablet2025.config;

import android.content.Context;
import com.tkf.tkftablet2025.BuildConfig;
import com.tkf.tkftablet2025.R;

/**
 * Application configuration class for managing URLs and other app settings.
 * This class provides a centralized way to manage configuration values
 * that may vary between different build variants or environments.
 */
public class AppConfig {

    // Environment types
    public enum Environment {
        DEVELOPMENT,
        PRODUCTION
    }

    private final Context context;
    private final Environment currentEnvironment;
    private final ServerSettingsManager serverSettingsManager;

    /**
     * Constructor for AppConfig
     * @param context Application context
     */
    public AppConfig(Context context) {
        this.context = context;
        this.currentEnvironment = determineEnvironment();
        this.serverSettingsManager = new ServerSettingsManager(context);
    }

    /**
     * Get the base URL for the web interface.
     * Priority: 1) User saved URL, 2) BuildConfig URL, 3) Resource-based URLs
     * @return Base URL string
     */
    public String getBaseUrl() {
        // First priority: Check if user has saved a custom URL
        String serverUrl = serverSettingsManager.getServerUrl();
        if (serverUrl != null && !serverUrl.trim().isEmpty()) {
            return serverUrl;
        }

        // Second priority: Use BuildConfig URL if available
        try {
            return BuildConfig.BASE_URL;
        } catch (Exception e) {
            // Third priority: Fallback to resource-based URLs
            return currentEnvironment == Environment.PRODUCTION
                    ? context.getString(R.string.production_base_url)
                    : context.getString(R.string.default_base_url);
        }
    }

    /**
     * Check if the app is running in debug mode
     * @return true if debug mode, false otherwise
     */
    public boolean isDebugMode() {
        return BuildConfig.DEBUG;
    }

    /**
     * Check if cache should be enabled
     * @return true if cache enabled, false otherwise
     */
    public boolean isCacheEnabled() {
        return !isDebugMode(); // Disable cache in debug mode for development
    }

    /**
     * Get user agent string for WebView
     * @return User agent string
     */
    public String getUserAgent() {
        return "TKFTablet2025/" + BuildConfig.VERSION_NAME + " (Android)";
    }

    /**
     * Determine the current environment based on build configuration
     * @return Environment enum
     */
    private Environment determineEnvironment() {
        if (BuildConfig.DEBUG) {
            return Environment.DEVELOPMENT;
        } else {
            // In a real app, you might check BuildConfig.BUILD_TYPE or other flags
            return Environment.PRODUCTION;
        }
    }

    /**
     * Check if JavaScript should be enabled in WebView
     * @return true if JavaScript should be enabled
     */
    public boolean isJavaScriptEnabled() {
        return true; // Always enabled for this app
    }

    /**
     * Check if DOM storage should be enabled in WebView
     * @return true if DOM storage should be enabled
     */
    public boolean isDomStorageEnabled() {
        return true; // Always enabled for this app
    }

    /**
     * Get the server settings manager instance
     * @return ServerSettingsManager instance
     */
    public ServerSettingsManager getServerSettingsManager() {
        return serverSettingsManager;
    }

}
