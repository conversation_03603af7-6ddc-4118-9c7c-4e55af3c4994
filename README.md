# TKF Tablet 2025

A modern Android tablet application that displays web content from a specific URL in fullscreen landscape mode.

## Features

- Displays web content from a specific URL
- Runs in landscape orientation and fullscreen mode
- Includes a reload button with cache clearing functionality
- Built with modern Android architecture components and best practices
- Uses Java 17 and AndroidX libraries

## Technical Details

- Minimum SDK: API 21 (Android 5.0 Lollipop)
- Target SDK: API 34 (Android 14)
- Language: Java 17
- Architecture: Single Activity with WebView
- Libraries:
  - AndroidX Core and AppCompat
  - Material Design 3
  - ConstraintLayout
  - Lifecycle components

## Setup

1. Clone the repository
2. Open the project in Android Studio
3. Build and run the application on a tablet device or emulator

## Configuration

The application uses a centralized configuration system through the `AppConfig` class:

### URL Configuration
- **Development**: Uses emulator localhost (********)
- **Production**: Uses production server (*************)

URLs are configured in:
1. `app/build.gradle` - BuildConfig fields for different build variants
2. `app/src/main/res/values/strings.xml` - Fallback URLs in resources

### Build Variants
- **debug**: Development build with logging enabled
- **release**: Production build optimized for performance

To change URLs, modify the `buildConfigField` values in `app/build.gradle`.

## License

This project is proprietary and confidential.

