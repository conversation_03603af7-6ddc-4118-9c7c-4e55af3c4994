package com.tkf.tkftablet2025.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Utility class for network operations and IP address retrieval
 */
public class NetworkUtils {
    
    private static final String TAG = "NetworkUtils";
    
    /**
     * Get all available IP addresses of the device
     * @param context Application context
     * @return List of IP addresses with their types
     */
    public static List<IPAddressInfo> getAllIPAddresses(Context context) {
        List<IPAddressInfo> ipAddresses = new ArrayList<>();
        
        // Get WiFi IP address
        String wifiIP = getWiFiIPAddress(context);
        if (wifiIP != null && !wifiIP.isEmpty()) {
            ipAddresses.add(new IPAddressInfo("WiFi", wifiIP, true));
        }
        
        // Get mobile data IP address
        String mobileIP = getMobileDataIPAddress();
        if (mobileIP != null && !mobileIP.isEmpty()) {
            ipAddresses.add(new IPAddressInfo("Mobile Data", mobileIP, true));
        }
        
        // Get all other network interfaces
        List<String> otherIPs = getAllNetworkInterfaceIPs();
        for (String ip : otherIPs) {
            if (!ip.equals(wifiIP) && !ip.equals(mobileIP)) {
                ipAddresses.add(new IPAddressInfo("Other", ip, false));
            }
        }
        
        return ipAddresses;
    }
    
    /**
     * Get WiFi IP address
     * @param context Application context
     * @return WiFi IP address or null if not available
     */
    public static String getWiFiIPAddress(Context context) {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null && wifiManager.isWifiEnabled()) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo != null) {
                    int ipAddress = wifiInfo.getIpAddress();
                    if (ipAddress != 0) {
                        return String.format("%d.%d.%d.%d",
                                (ipAddress & 0xff),
                                (ipAddress >> 8 & 0xff),
                                (ipAddress >> 16 & 0xff),
                                (ipAddress >> 24 & 0xff));
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting WiFi IP address", e);
        }
        return null;
    }
    
    /**
     * Get mobile data IP address
     * @return Mobile data IP address or null if not available
     */
    public static String getMobileDataIPAddress() {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                // Look for mobile data interfaces (usually rmnet, ccmni, etc.)
                String name = intf.getName().toLowerCase();
                if (name.contains("rmnet") || name.contains("ccmni") || name.contains("pdp")) {
                    List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
                    for (InetAddress addr : addrs) {
                        if (!addr.isLoopbackAddress() && !addr.isLinkLocalAddress()) {
                            String sAddr = addr.getHostAddress();
                            if (sAddr != null && isIPv4Address(sAddr)) {
                                return sAddr;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting mobile data IP address", e);
        }
        return null;
    }
    
    /**
     * Get all network interface IP addresses
     * @return List of IP addresses
     */
    public static List<String> getAllNetworkInterfaceIPs() {
        List<String> ipAddresses = new ArrayList<>();
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
                for (InetAddress addr : addrs) {
                    if (!addr.isLoopbackAddress()) {
                        String sAddr = addr.getHostAddress();
                        if (sAddr != null && isIPv4Address(sAddr)) {
                            ipAddresses.add(sAddr);
                        }
                    }
                }
            }
        } catch (SocketException e) {
            Log.e(TAG, "Error getting network interface IPs", e);
        }
        return ipAddresses;
    }
    
    /**
     * Check if the device is connected to WiFi
     * @param context Application context
     * @return true if connected to WiFi
     */
    public static boolean isWiFiConnected(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
                return networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
            }
        }
        return false;
    }
    
    /**
     * Check if the device is connected to mobile data
     * @param context Application context
     * @return true if connected to mobile data
     */
    public static boolean isMobileDataConnected(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
                return networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR);
            }
        }
        return false;
    }
    
    /**
     * Check if a string is a valid IPv4 address
     * @param ip IP address string
     * @return true if valid IPv4 address
     */
    private static boolean isIPv4Address(String ip) {
        return ip.matches("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$");
    }
    
    /**
     * Class to hold IP address information
     */
    public static class IPAddressInfo {
        private final String type;
        private final String address;
        private final boolean isPrimary;
        
        public IPAddressInfo(String type, String address, boolean isPrimary) {
            this.type = type;
            this.address = address;
            this.isPrimary = isPrimary;
        }
        
        public String getType() {
            return type;
        }
        
        public String getAddress() {
            return address;
        }
        
        public boolean isPrimary() {
            return isPrimary;
        }
        
        @Override
        public String toString() {
            return type + ": " + address;
        }
    }
}
